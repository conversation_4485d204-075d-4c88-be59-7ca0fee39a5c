apiVersion: v1
kind: Service
metadata:
  name: supabase-test-serv
  labels:
    app: supabase-test
  annotations:
    tke.cloud.tencent.com/networks: tke-route-eni
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: api
    port: 3000
    targetPort: 3000
    protocol: TCP
  - name: db
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: supabase-test
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-test-deployment
  namespace: lightbox-srv-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ facility }}-{{ project_configuration.label_name }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ facility }}-{{ project_configuration.label_name }}
      annotations:
        tke.cloud.tencent.com/networks: tke-route-eni
    spec:
      containers:
      - image: {{ main_configuration.docker_registry }}/lightbox/{{ project_configuration.app_name }}:{{ project_configuration.version }}
        name: {{ project_configuration.label_name }}
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
          name: http
        - containerPort: 3000
          name: api
        - containerPort: 5432
          name: db
        workingDir: "/"
        resources:
          limits:
            cpu: 2000m
            memory: 2Gi
            tke.cloud.tencent.com/eni-ip: "1"
          requests:
            cpu: 1000m
            memory: 1Gi
            tke.cloud.tencent.com/eni-ip: "1"
        env:
{%- for item in facility_project_configuration.env %}
          - name: {{ item.env_key }}
            value: {{ item.env_value }}
{%- endfor %}
          - name: POSTGRES_PASSWORD
            valueFrom:
              secretKeyRef:
                key: POSTGRES_PASSWORD
                name: {{ facility }}-{{ project_configuration.label_name }}-secret
        volumeMounts:
        - name: supabase-studio-pvc
          mountPath: /var/lib/supabase/data
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
          subPath: postgres
        - name: kong-pvc
          mountPath: /var/lib/kong
          subPath: kong
        - name: auth-storage
          mountPath: /var/lib/supabase/auth
          subPath: auth
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: supabase-studio-pvc
        persistentVolumeClaim:
          claimName: {{ facility }}-supabase-studio-pvc
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: {{ facility }}-supabase-postgres-pvc
      - name: kong-pvc
        persistentVolumeClaim:
          claimName: {{ facility }}-supabase-kong-pvc
      - name: auth-storage
        persistentVolumeClaim:
          claimName: {{ facility }}-supabase-auth-pvc
      imagePullSecrets:
      - name: docker-ci
---
# Persistent Volume Claims
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ facility }}-supabase-postgres-pvc
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ facility }}-supabase-studio-pvc
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ facility }}-supabase-kong-pvc
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ facility }}-supabase-auth-pvc
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 30Gi

---
apiVersion: v1
kind: Secret
metadata:
  name: {{ facility }}-{{ project_configuration.label_name }}-secret
data:
  POSTGRES_PASSWORD: QXoxMjMhc3NkMTI0eno=
  POSTGRES_URL: {{ b64enc('postgres://supabase:Az123!ssd124zz@localhost:5432/supabase') | indent(2) }}
  STUDIO_CONFIG: {{ b64enc('base-config.json') | indent(2) }}

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ facility }}-{{ project_configuration.label_name }}-ingress
  annotations:
    kubernetes.io/ingress.class: ingress-controller-37898
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
  - host: {{ project_configuration.domain }}
    http:
      paths:
      - path: /api(/|$)(.*)
        pathType: Prefix
        backend:
          serviceName: {{ facility }}-{{ project_configuration.label_name }}-serv
          servicePort: 3000
      - path: /($|)(.*)
        pathType: Prefix
        backend:
          serviceName: {{ facility }}-{{ project_configuration.label_name }}-serv
          servicePort: 80
