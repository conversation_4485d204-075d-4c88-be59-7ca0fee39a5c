apiVersion: v1
kind: Service
metadata:
  name: supabase-simple-serv
  namespace: lightbox-idc-1
  labels:
    app: supabase-simple
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: api
    port: 3000
    targetPort: 3000
    protocol: TCP
  - name: db
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: supabase-simple
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-simple-deployment
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-simple
  template:
    metadata:
      labels:
        app: supabase-simple
    spec:
      containers:
      - image: supabase/studio:20250130-b048539
        name: supabase-simple
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
          name: http
        - containerPort: 3000
          name: api
        - containerPort: 5432
          name: db
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi
        env:
        - name: POSTGRES_PASSWORD
          value: "Az123!ssd124zz"
        - name: POSTGRES_USER
          value: "supabase"
        - name: POSTGRES_DB
          value: "supabase"
        # 使用临时存储，不挂载PVC
        volumeMounts:
        - name: temp-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: temp-storage
        emptyDir: {}
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: supabase-simple-ingress
  namespace: lightbox-idc-1
  annotations:
    kubernetes.io/ingress.class: ingress-controller-37898
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
  - host: api-docs.woa.com
    http:
      paths:
      - path: /supabase/api(/|$)(.*)
        backend:
          serviceName: supabase-simple-serv
          servicePort: 3000
      - path: /supabase(/|$)(.*)
        backend:
          serviceName: supabase-simple-serv
          servicePort: 80
