# test-persistence.ps1
Write-Host "=== Supabase持久化存储验证测试 ===" -ForegroundColor Green

# 检查Podman是否安装
try {
    podman --version
    Write-Host "✓ Podman已安装" -ForegroundColor Green
} catch {
    Write-Host "✗ 请先安装Podman" -ForegroundColor Red
    exit 1
}

# 1. 清理环境
Write-Host "1. 清理环境..." -ForegroundColor Yellow
podman stop supabase-test 2>$null
podman rm supabase-test 2>$null

# 2. 创建本地持久化卷
Write-Host "2. 创建持久化卷..." -ForegroundColor Yellow
podman volume create supabase-postgres-data 2>$null
podman volume create supabase-studio-data 2>$null
podman volume create supabase-kong-data 2>$null
podman volume create supabase-auth-data 2>$null

# 3. 运行容器
Write-Host "3. 启动Supabase容器..." -ForegroundColor Yellow
podman run -d `
  --name supabase-test `
  -p 3000:3000 -p 5432:5432 -p 8080:80 `
  -v supabase-postgres-data:/var/lib/postgresql/data `
  -v supabase-studio-data:/var/lib/supabase/data `
  -v supabase-kong-data:/var/lib/kong `
  -v supabase-auth-data:/var/lib/supabase/auth `
  -e POSTGRES_PASSWORD=Az123!ssd124zz `
  supabase/studio:20250130-b048539

# 4. 等待服务启动
Write-Host "4. 等待服务启动（60秒）..." -ForegroundColor Yellow
Start-Sleep -Seconds 60

# 5. 检查容器状态
Write-Host "5. 检查容器状态..." -ForegroundColor Yellow
podman ps --filter name=supabase-test

# 6. 创建测试数据
Write-Host "6. 创建测试数据..." -ForegroundColor Yellow
podman exec -it supabase-test psql -U supabase -d supabase -c "
CREATE TABLE IF NOT EXISTS test_persistence (
  id SERIAL PRIMARY KEY,
  data TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
INSERT INTO test_persistence (data) VALUES ('test data 1');
SELECT * FROM test_persistence;
"

Write-Host "7. 第一阶段测试完成！" -ForegroundColor Green
Write-Host "容器正在运行，你可以：" -ForegroundColor Cyan
Write-Host "- 访问 http://localhost:3000 查看Supabase Studio" -ForegroundColor Cyan
Write-Host "- 使用 podman exec -it supabase-test bash 进入容器" -ForegroundColor Cyan
Write-Host "- 运行 podman stop supabase-test 停止容器" -ForegroundColor Cyan