apiVersion: v1
kind: Service
metadata:
  name: supabase-test-serv
  namespace: lightbox-idc-1
  labels:
    app: supabase-test
  annotations:
    tke.cloud.tencent.com/networks: tke-route-eni
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: api
    port: 3000
    targetPort: 3000
    protocol: TCP
  - name: db
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: supabase-test
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-test-deployment
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-test
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: supabase-test
      annotations:
        tke.cloud.tencent.com/networks: tke-route-eni
    spec:
      containers:
      - image: supabase/studio:20250130-b048539
        name: supabase-test
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
          name: http
        - containerPort: 3000
          name: api
        - containerPort: 5432
          name: db
        workingDir: "/"
        resources:
          limits:
            cpu: 2000m
            memory: 2Gi
            tke.cloud.tencent.com/eni-ip: "1"
          requests:
            cpu: 1000m
            memory: 1Gi
            tke.cloud.tencent.com/eni-ip: "1"
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: POSTGRES_PASSWORD
              name: supabase-test-secret
        - name: POSTGRES_USER
          value: 'supabase'
        - name: POSTGRES_HOST
          value: 'localhost'
        - name: POSTGRES_PORT
          value: '5432'
        - name: POSTGRES_DB
          value: 'supabase'
        - name: SUPABASE_ANON_KEY
          value: 'your-anon-key'
        - name: SUPABASE_SERVICE_KEY
          value: 'your-service-key'
        - name: STUDIO_PORT
          value: '80'
        - name: API_PORT
          value: '3000'
        volumeMounts:
        - name: supabase-studio-pvc
          mountPath: /var/lib/supabase/data
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
          subPath: postgres
        - name: kong-pvc
          mountPath: /var/lib/kong
          subPath: kong
        - name: auth-storage
          mountPath: /var/lib/supabase/auth
          subPath: auth
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: supabase-studio-pvc
        persistentVolumeClaim:
          claimName: supabase-studio-pvc
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: supabase-postgres-pvc
      - name: kong-pvc
        persistentVolumeClaim:
          claimName: supabase-kong-pvc
      - name: auth-storage
        persistentVolumeClaim:
          claimName: supabase-auth-pvc
      imagePullSecrets:
      - name: docker-ci
---
# Persistent Volume Claims
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-postgres-pvc
  namespace: lightbox-idc-1
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-studio-pvc
  namespace: lightbox-idc-1
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-kong-pvc
  namespace: lightbox-idc-1
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-auth-pvc
  namespace: lightbox-idc-1
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 30Gi

---
apiVersion: v1
kind: Secret
metadata:
  name: supabase-test-secret
  namespace: lightbox-idc-1
data:
  POSTGRES_PASSWORD: QXoxMjMhc3NkMTI0eno=
  POSTGRES_URL: cG9zdGdyZXM6Ly9zdXBhYmFzZTpBejEyMyFzc2QxMjR6ekBsb2NhbGhvc3Q6NTQzMi9zdXBhYmFzZQ==
  STUDIO_CONFIG: YmFzZS1jb25maWcuanNvbg==

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: supabase-test-ingress
  namespace: lightbox-idc-1
  annotations:
    kubernetes.io/ingress.class: ingress-controller-37898
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
  - host: api-docs.woa.com
    http:
      paths:
      - path: /supabase/api(/|$)(.*)
        backend:
          serviceName: supabase-test-serv
          servicePort: 3000
      - path: /supabase(/|$)(.*)
        backend:
          serviceName: supabase-test-serv
          servicePort: 80
